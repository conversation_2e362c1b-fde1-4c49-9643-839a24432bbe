<template>
  <div class='dialogContainer' v-show="dialogVisible" v-resizer>
    <Dialog :title="dialogTitle" v-model="dialogVisible" :width="mobile ? '100%' : '80%'" fullscreen v-dialogDrag>
      <el-form ref="formRef" :model="abnormalData" :rules="formRules" label-width="120px">
        <el-card class="mb-4" shadow="never">
          <!-- 全选按钮 -->
          <div v-if="!mobile" style="margin-bottom: 10px;">
            <el-button @click="addNewRow" type="primary" size="small">
              新增
            </el-button>
            <span style="margin-left: 10px; color: #666;">
              已选择 {{ selectedRows.length }} 项
            </span>
          </div>
            <!-- 批量赋值按钮（移动端） -->
  <div v-if="mobile" class="mobile-batch-container">
    <div class="batch-assignment-sticky">
    <el-button @click="toggleSelectAll"  size="small">
      {{ isAllSelected ? '取消' : '全选' }}
    </el-button>
      <el-button @click="batchAssignment" type="primary" size="small">批量赋值</el-button>
    </div>
  </div>
          <div v-if="mobile" class="card-list-container">
            <div v-for="(row, index) in abnormalData" :key="index" class="card-item">
              <div class="card-title">
                 <el-checkbox v-model="row._checked" @click.stop style="top: 2px;"  @change="(value) => handleCheckboxChange(row, Boolean(value))" />
                第 {{ index + 1 }} 条 / 共 {{ abnormalData.length }} 条 <span>
                </span>
              </div>
              <el-form class="mobile-body-form">
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="时间段">
                      <div class="time-range-row">
                        <div v-if="row.isNew">
                        <el-time-select
v-model="row.productionTimeStart" placeholder="起始时间" :start="'08:30'"
                          :end="'23:30'" :step="'00:15'" placement="top-start" />
                        <span class="time-range-separator">-</span>
                        <el-time-select
v-model="row.productionTimeEnd" placeholder="结束时间" :start="'08:30'"
                          :end="'23:30'" :step="'00:15'" :min-time="row.productionTimeStart" placement="top-start" />
                          </div>
                          <div v-else>
                            {{ row.productionTimeStart }}-{{ row.productionTimeEnd }}
                          </div>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="工单号">
                      <div v-if="row.isNew">
                      <el-autocomplete
v-model="row.productionOrderCode"
                        :fetch-suggestions="(queryString, cb) => queryWorkSalesOptions(queryString, cb, 0)"
                        placeholder="输入工单号" style="width: 100%"
                        @select="(item) => handleProductionOrderSelect(item, index)" :trigger-on-focus="false"
                        :debounce="300" popper-class="production-order-autocomplete" placement="top-start">
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text">{{ item.productionOrderCode }}-{{ item.productNo }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      </div>
                                      <div v-else>
                  {{ row.productionOrderCode }}
                </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="销售订单号">
                      <div v-if="row.isNew">
                      <el-autocomplete
v-model="row.salesOrderCode"
                        :fetch-suggestions="(queryString, cb) => queryWorkSalesOptions(queryString, cb, 1)"
                        placeholder="输入销售订单号" style="width: 100%"
                        @select="(item) => handleSalesOrderSelect(item, index)" :trigger-on-focus="false"
                        :debounce="300" popper-class="production-order-autocomplete" placement="top-start">
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text">{{ item.salesOrderCode }}-{{ item.productNo }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      </div>
                                      <div v-else>
                  {{ row.salesOrderCode }}
                </div>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="品号">
                      <div v-if="row.isNew">
                      <el-autocomplete
v-model="row.productNo" :fetch-suggestions="(queryString, cb) =>
                        queryProductNo(
                          queryString,
                          cb,
                          row.productionOrderCode,
                          row.salesOrderCode
                        )
                        " placeholder="输入品号查询" style="width: 100%"
                        @select="(item) => handleSalesOrderSelect(item, index)" :trigger-on-focus="false"
                        :debounce="300" popper-class="production-order-autocomplete" placement="top-start">
                        <template #default="{ item }">
                          <div class="autocomplete-item">
                            <div class="main-text">{{ item.productNo }}</div>
                          </div>
                        </template>
                      </el-autocomplete>
                      </div>
                    <div v-else>
                  {{ row.productNo }}
                </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="品名">
                      {{ row.modelsOrColor }}
                    </el-form-item>
                  </el-col>

                </el-row>
                <el-row>
                  <el-col :span="12">
                    <el-form-item label="数量">
                      {{ row.hoursReportNum }}
                    </el-form-item>
                  </el-col>
                   <el-col :span="12">
                    <el-form-item label="单位">
                      {{ row.units }}
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="24">
                    <el-form-item label="问题点">
                      <el-input v-model="row.abnormalRemark" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="24">
                    <el-form-item label="临时对策">
                      <el-input v-model="row.abnormalCountermeasures"  />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" >
                    <el-form-item label="人数">
                      <el-input v-model="row.abnormalNum" min="0" @input="(val) => row.abnormalNum = Number(val)" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12" >
                    <el-form-item label="总工时">
                      <el-input v-model="row.abnormalWork" type="number" :min="0" @input="(val) => batchForm.abnormalWork = Number(val)"  />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="10">
                    <el-col :span="12">
                    <el-button type="primary"  plain @click.stop="addNewRow"  class="w-full">
                      新增
                    </el-button>
                    </el-col>
                  <el-col :span="12">
                    <el-button type="danger"  plain @click="removeBodyRow(index)" class="w-full">
                      删除
                    </el-button>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </div>
          <el-table
v-else :data="abnormalData" border stripe scrollbar-always-on ref="tableRef"
            @selection-change="handleSelectionChange" @row-contextmenu="rightClick" @row-click="clickTableRow"
            style="min-height: 250px;height:calc(100vh - 455px)">
            ">
            <el-table-column type="selection" width="55" align="center"  fixed="left" />
            <el-table-column label="时间段" min-width="200">
              <template #default="{ row }">
                <div v-if="row.isNew" class="time-range-row">
                  <el-time-select
v-model="row.productionTimeStart" placeholder="起始时间" :start="'08:30'" :end="'23:30'"
                     :step="'00:15'" placement="top-start" />
                  <span class="time-range-separator">-</span>
                  <el-time-select
v-model="row.productionTimeEnd" placeholder="结束时间" :start="'08:30'" :end="'23:30'"
                     :step="'00:15'" :min-time="row.productionTimeStart" placement="top-start" />
                </div>
                <div v-else>
                  {{ row.productionTimeStart }}-{{ row.productionTimeEnd }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="工单号" min-width="160" prop="productionOrderCode">
              <template #default="{ row, $index }">
                <div v-if="row.isNew">
                  <el-autocomplete
v-model="row.productionOrderCode" 
                    :fetch-suggestions="(queryString, cb) => queryWorkSalesOptions(queryString, cb, 0)"
                    placeholder="输入工单号" style="width: 100%"
                    @select="(item) => handleProductionOrderSelect(item, $index)" :trigger-on-focus="false"
                    :debounce="300" popper-class="production-order-autocomplete" placement="top-start">
                    <template #default="{ item }">
                      <div class="autocomplete-item">
                        <div class="main-text">{{ item.productionOrderCode }}-{{ item.productNo }}</div>
                      </div>
                    </template>
                  </el-autocomplete>
                </div>
                <div v-else>
                  {{ row.productionOrderCode }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="销售订单号" min-width="160" prop="salesOrderCode">
              <template #default="{ row, $index }">
                <div v-if="row.isNew">
                  <el-autocomplete
v-model="row.salesOrderCode" 
                    :fetch-suggestions="(queryString, cb) => queryWorkSalesOptions(queryString, cb, 1)"
                    placeholder="输入销售订单号" style="width: 100%" @select="(item) => handleSalesOrderSelect(item, $index)"
                    :trigger-on-focus="false" :debounce="300" popper-class="production-order-autocomplete"
                    placement="top-start">
                    <template #default="{ item }">
                      <div class="autocomplete-item">
                        <div class="main-text">{{ item.salesOrderCode }}-{{ item.productNo }}</div>
                      </div>
                    </template>
                  </el-autocomplete>
                </div>
                <div v-else>
                  {{ row.salesOrderCode }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="品号" min-width="100" prop="productNo">
              <template #default="{ row, $index }">
                <div v-if="row.isNew">
                  <el-autocomplete
v-model="row.productNo" :fetch-suggestions="(queryString, cb) =>
                    queryProductNo(queryString, cb, row.productionOrderCode, row.salesOrderCode)
                    " placeholder="输入品号查询" style="width: 100%" @select="(item) => handleSalesOrderSelect(item, $index)"
                    :trigger-on-focus="false" :debounce="300" popper-class="production-order-autocomplete"
                    placement="top-start">
                    <template #default="{ item }">
                      <div class="autocomplete-item">
                        <div class="main-text">{{ item.productNo }}</div>
                      </div>
                    </template>
                  </el-autocomplete>
                </div>
                <div v-else>
                  {{ row.productNo }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="机型/颜色（品名）" min-width="120" prop="modelsOrColor">
              <template #default="{ row }">
                {{ row.modelsOrColor }}
              </template>
            </el-table-column>
            <el-table-column label="数量" min-width="60" prop="hoursReportNum">
              <template #default="{ row }">
                {{ row.hoursReportNum }}
              </template>
            </el-table-column>
            <el-table-column label="单位" min-width="30" prop="units">
              <template #default="{ row }">
                {{ row.units }}
              </template>
            </el-table-column>
            <el-table-column label="问题点" min-width="200" prop="abnormalRemark">
              <template #default="{ row }">
                <el-input
v-model="row.abnormalRemark" type="textarea" placeholder="请输入问题点"
                  @keydown.enter="handleAutoNumber($event, row, 'abnormalRemark')"
                  @focus="initializeTextContent(row, 'abnormalRemark')" style="width: 100%;max-height: 50px;" />
              </template>
            </el-table-column>

            <el-table-column label="临时对策" min-width="200" prop="abnormalCountermeasures">
              <template #default="{ row }">
                <el-input
v-model="row.abnormalCountermeasures" type="textarea" placeholder="请输入临时对策"
                  @keydown.enter="handleAutoNumber($event, row, 'abnormalCountermeasures')"
                  @focus="initializeTextContent(row, 'abnormalCountermeasures')"
                  style="width: 100%; max-height: 50px;" />
              </template>
            </el-table-column>
            <el-table-column label="人数" min-width="50" prop="abnormalNum">
              <template #default="{ row }">
                <el-input
v-model="row.abnormalNum" type="number" min="0" class="no-spin-input"
                  @input="(val) => row.abnormalNum = Number(val)" />
              </template>
            </el-table-column>
            <el-table-column label="总工时" min-width="50" prop="abnormalWork">
              <template #default="{ row }">
                <el-input
v-model="row.abnormalWork" type="number" class="no-spin-input"
                  @input="(val) => row.abnormalWork = Number(val)" />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="80" fixed="right">
              <template #default="{ $index }">
                <el-button type="danger" link @click="removeBodyRow($index)"> 删除 </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 右键菜单 -->
          <div id="contextMenu" class="context-menu" style="position: fixed; display: none; z-index: 1000;">
            <ul class="menu-list">
              <li @click="batchAssignValues" class="menu-item">
                列批量赋值
              </li>
              <li @click="batchDeleteValues" class="menu-item">
                批量删除
              </li>
            </ul>
          </div>
        </el-card>
      </el-form>
      <template #footer>
        <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </template>
    </Dialog>

    <el-dialog v-model="batchDialogVisible" title="批量赋值" width="400px">
  <el-form :model="batchForm" label-width="90px">
    <el-form-item label="问题点">
      <el-input v-model="batchForm.abnormalRemark" />
    </el-form-item>
    <el-form-item label="临时对策">
      <el-input v-model="batchForm.abnormalCountermeasures" />
    </el-form-item>
    <el-form-item label="人数">
      <el-input v-model="batchForm.abnormalNum" type="number" :min="0" @input="(val) => batchForm.abnormalNum = Number(val)"/>
    </el-form-item>
    <el-form-item label="总工时">
      <el-input v-model="batchForm.abnormalWork" type="number" :min="0" @input="(val) => batchForm.abnormalWork = Number(val)"  />
    </el-form-item>
  </el-form>
  <template #footer>
    <el-button @click="batchDialogVisible = false">取 消</el-button>
    <el-button type="primary" @click="confirmBatchAssignment">确 定</el-button>
  </template>
</el-dialog>
  </div>

</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { useAppStore } from '@/store/modules/app'
import { DayApi, AbnormalDayVO } from '@/api/report/technology/production'
import { type } from '../../../../../types/auto-imports';
import { size } from '../../../../bi/information/componets/Main.vue';

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)

const formRules = reactive({
  number: [{ required: true, message: '请输入人数', trigger: 'blur' }],
  abnormalFormTimeStart: [{ required: true, message: '请选择起始时间', trigger: 'change' }],
  abnormalFormTimeEnd: [{ required: true, message: '请选择结束时间', trigger: 'change' }]
})
const formRef = ref() // 表单 Ref

const bodyData = ref({
  id: 0,
  abnormalNum: 0, //异常人数
  abnormalWork: 0,//异常工时
  abnormalRemark: '',//异常问题点
  abnormalCountermeasures: ''//异常对策
})

// 表身数据（其他字段）
const abnormalData = ref<any[]>([])


// 选中的行数据
const selectedRows = ref<any[]>([])

// 表格引用
const tableRef = ref()

// 处理选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

//移动端处理选择变化
const handleCheckboxChange = (row: any, checked: boolean) => {
  if (checked) {
    // 如果选中，加入 selectedRows
    if (!selectedRows.value.includes(row)) {
      selectedRows.value.push(row)
    }
  } else {
    // 如果取消选中，从 selectedRows 中移除
    selectedRows.value = selectedRows.value.filter(item => item !== row)
  }
}
// 获取工单销售订单数据
const queryWorkSalesOptions = (queryString: string, cb: Function, type: number) => {
  if (!queryString) {
    cb([])
    return
  }

  const placeholderText = type === 0 ? '生产工单号' : '销售订单号'
  const loadingItem = { [type === 0 ? 'productionOrderCode' : 'salesOrderCode']: '加载中...', loading: true }
  const noDataItem = { [type === 0 ? 'productionOrderCode' : 'salesOrderCode']: '暂无数据', noData: true }
  const errorItem = { [type === 0 ? 'productionOrderCode' : 'salesOrderCode']: '查询失败', error: true }

  // 先同步返回加载状态
  cb([loadingItem])

  DayApi.getWorkSalesOptions(queryString, type)
    .then((response) => {
      const suggestions = response || []
      cb(suggestions.length > 0 ? suggestions : [noDataItem])
    })
    .catch((error) => {
      console.error(`查询${placeholderText}失败:`, error)
      cb([errorItem])
    })
}
//选择工单号后
const handleProductionOrderSelect = async (item: any, rowIndex: number) => {
  const row = abnormalData.value[rowIndex]
  if (row && item) {
    row.productionOrderCode = item.productionOrderCode
    row.salesOrderCode = item.salesOrderCode
    row.productNo = item.productNo
    row.modelsOrColor = item.modelsOrColor
    row.workOrderNum = item.workOrderNum
    row.units = item.units
  }
}

// 查询品号
const queryProductNo = (
  queryString: string,
  cb: Function,
  productionOrderCode: string,
  salesOrderCode: string
) => {
  if (!queryString) {
    cb([])
    return
  }
  // 先同步返回加载状态，避免下拉框立即关闭
  cb([{ productNo: '加载中...', loading: true }])
  //把工单号和销售订单号和输入的品号都传给后端
  const data = {
    productionOrderCode: productionOrderCode, // 生产工单号
    salesOrderCode: salesOrderCode, // 销售订单号
    productNo: queryString // 输入的品号
  }
  DayApi.getProductNo(data)
    .then((response) => {
      const suggestions = response || []
      // 直接返回后端数据，因为格式已经符合要求
      cb(suggestions.length > 0 ? suggestions : [{ productNo: '暂无数据', noData: true }])
    })
    .catch((error) => {
      console.error('查询销售订单号失败:', error)
      cb([{ productNo: '查询失败', error: true }])
    })
}

// 选择销售订单号后的处理
const handleSalesOrderSelect = async (item: any, rowIndex: number) => {
  const row = abnormalData.value[rowIndex]
  // 设置生产工单号
  row.productionOrderCode = item.productionOrderCode
  row.salesOrderCode = item.salesOrderCode
  row.productNo = item.productNo
  row.modelsOrColor = item.modelsOrColor
  row.workOrderNum = item.workOrderNum
  row.units = item.units
}
// 新增可编辑行
const addNewRow = () => {
  const newRow = createEditableRow()
  abnormalData.value.push(newRow)
}
const isAllSelected = ref(false)
const toggleSelectAll = () => {
  const checked = !isAllSelected.value
  isAllSelected.value = checked

  abnormalData.value.forEach(row => {
    row._checked = checked
  })

  if (checked) {
    selectedRows.value = [...abnormalData.value]
  } else {
    selectedRows.value = []
  }
}
const batchAssignment =()=>{
  if(selectedRows.value.length===0){
    message.error('请先选择行')
    return
  }
    // 初始化表单数据
  batchForm.value = {
    abnormalRemark: '',
    abnormalCountermeasures: '',
    abnormalNum: 0,
    abnormalWork: 0
  }
  batchDialogVisible.value = true
}

// 批量赋值表单数据
const batchForm = ref({
  abnormalRemark: '',
  abnormalCountermeasures: '',
  abnormalNum:  0 ,
  abnormalWork: 0 
})

const confirmBatchAssignment = () => {
  const { abnormalRemark, abnormalCountermeasures, abnormalNum, abnormalWork } = batchForm.value

  selectedRows.value.forEach(row => {
    if (abnormalRemark !== '') row.abnormalRemark = abnormalRemark
    if (abnormalCountermeasures !== '') row.abnormalCountermeasures = abnormalCountermeasures
    if (abnormalNum !== null) row.abnormalNum = abnormalNum
    if (abnormalWork !== null) row.abnormalWork = abnormalWork
  })

  message.success(`已对 ${selectedRows.value.length} 行进行批量赋值`)
  batchDialogVisible.value = false
}

// 控制弹窗显示
const batchDialogVisible = ref(false)
//新增行
const createEditableRow = () => ({
  dateStr:dateStrAbnormal.value,
  productionLine:productionLineAbnormal.value,
  productionTime: '',
  productionTimeStart: '',
  productionTimeEnd: '',
  productionOrderCode: '',
  salesOrderCode: '',
  productNo: '',
  modelsOrColor: '',
  workOrderNum: '',
  units: '',
  abnormalRemark: '',
  abnormalCountermeasures: '',
  abnormalNum: 0,
  abnormalWork: 0,
  isNew: true // 标记为新增行
})

// 删除行时清理监听
const removeBodyRow = (index: number) => {
  // 删除行
  abnormalData.value.splice(index, 1)
}
const batchesIdAbnormal = ref()

const dateStrAbnormal=ref()
const productionLineAbnormal=ref()
/** 打开弹窗 */
const openForm = async (row: any, title?: any, batchesId?: any,dateStr?: any,productionLine?: any) => {
  batchesIdAbnormal.value = batchesId
  dialogVisible.value = true
  dialogTitle.value = title
  dateStrAbnormal.value=dateStr
  productionLineAbnormal.value=productionLine
  if (row.value.length > 0) {
    abnormalData.value = row.value.map(item => ({ ...item, isNew: false })) // 来自父页面，只读
  } 
  const res=await DayApi.getAbnormalByBatchesId(batchesId)
  if(res.length>0){
 const newData = res.map((item: any) => {
      // 如果 item.productionTime 存在，则拆分它
      const [productionTimeStart = '', productionTimeEnd = ''] = (item.productionTime || '').split('-')

      return {
        ...item,
        productionTimeStart,
        productionTimeEnd,
        isNew: true
      }
    })
    abnormalData.value = [...abnormalData.value, ...newData]
  }
  console.log(abnormalData.value)
  // 清空已选中的行
  selectedRows.value = []
}
defineExpose({ open, openForm }) // 提供 open 方法，用于打开弹窗


const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调

/** 提交表单 */
const submitForm = async () => {
  // 提交请求
  formLoading.value = true
  try {
  let isValid = true;
for (let i = 0; i < abnormalData.value.length; i++) {
  const item = abnormalData.value[i];
  if ( !item.productNo || !item.productionTimeStart || !item.productionTimeEnd) {
    message.error(`第 ${i + 1} 行信息不完整，请检查！`);
    isValid = false;
    break;
  }
}
  if(isValid){
    formLoading.value = false
    //关闭弹窗
    dialogVisible.value = false
      //判断是否有在本页面新增的异常工时，如果有则存入数据库中
      const newData=abnormalData.value.filter(item => item.isNew)
      if(newData.length>0){
        newData.forEach(item => {
          item.dateStr=dateStrAbnormal.value
          item.productionLine=productionLineAbnormal.value
          item.batchesId=batchesIdAbnormal.value
          item.workType= 1
          item.isAbnormal= 0
          item.productionTime = item.productionTimeStart + '-' + item.productionTimeEnd
        })
        const res=await DayApi.createAbnormalDay(newData)
          abnormalData.value.forEach(item => {
          item.batchesId = res
        })
      }else{
        //搞一条空数据，把批次ID塞进去，让后端删除掉数据
        const emptyData=[{
          batchesId:batchesIdAbnormal.value,
          isAbnormal: 1
        }]
        await DayApi.createAbnormalDay(emptyData)
        abnormalData.value.forEach(item => {
          item.batchesId = batchesIdAbnormal.value
        })
      }

    emit('success', abnormalData.value)
    message.success('操作成功')
  }
  } finally {
    formLoading.value = false
  }

}



// 处理自动编号功能
const handleAutoNumber = (event: KeyboardEvent, row: any, property: string) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()

    const textarea = event.target as HTMLTextAreaElement
    const cursorPosition = textarea.selectionStart
    const currentValue = row[property] || ''

    // 分析当前内容，找到最后一个序号
    const lines = currentValue.split('\n')
    let lastNumber = 0

    // 查找最后一个有效的序号
    for (let i = lines.length - 1; i >= 0; i--) {
      const match = lines[i].match(/^(\d+)\.\s*/)
      if (match) {
        lastNumber = parseInt(match[1])
        break
      }
    }

    const nextNumber = lastNumber + 1
    const newLine = `\n${nextNumber}. `

    // 在光标位置插入新的编号行
    const beforeCursor = currentValue.substring(0, cursorPosition)
    const afterCursor = currentValue.substring(cursorPosition)

    const newValue = beforeCursor + newLine + afterCursor
    row[property] = newValue

    // 设置新的光标位置
    nextTick(() => {
      const newCursorPosition = cursorPosition + newLine.length
      textarea.setSelectionRange(newCursorPosition, newCursorPosition)
      textarea.focus()
    })
  }
}

// 初始化文本框内容（如果为空则添加第一个序号）
const initializeTextContent = (row: any, property: string) => {
  if (!row[property] || row[property].trim() === '') {
    row[property] = '1. '
  }
}

// 右键菜单相关
const currentRow = ref<any>(null)
const currentColumn = ref<any>(null)

// 右键点击事件
const rightClick = (row: any, column: any, event: MouseEvent) => {
  currentRow.value = row
  currentColumn.value = column

  const menu = document.getElementById('contextMenu')
  if (!menu) return

  event.preventDefault()

  menu.style.left = `${event.clientX + 10}px`
  menu.style.top = `${event.clientY - 10}px`
  menu.style.display = 'block'
}

// 表格左键点击事件（隐藏菜单）
const clickTableRow = () => {
  const menu = document.getElementById('contextMenu') as HTMLElement
  if (menu) {
    menu.style.display = 'none'
  }
}

// 批量赋值功能
const batchAssignValues = () => {
  // 检查是否有选中的行
  if (selectedRows.value.length === 0) {
    message.error('请先选择行')
    return
  }

  if (!currentRow.value) {
    message.error('请先右键选择一个单元格')
    return
  }
  message.confirm('确定要批量赋值选中的行吗？').then(() => { 
  // 获取当前行的四个字段值
  const sourceValues = {
    abnormalRemark: currentRow.value.abnormalRemark,
    abnormalCountermeasures: currentRow.value.abnormalCountermeasures,
    abnormalNum: currentRow.value.abnormalNum,
    abnormalWork: currentRow.value.abnormalWork
  }

  // 批量赋值给选中的行
  selectedRows.value.forEach(row => {
    row.abnormalRemark = sourceValues.abnormalRemark
    row.abnormalCountermeasures = sourceValues.abnormalCountermeasures
    row.abnormalNum = sourceValues.abnormalNum
    row.abnormalWork = sourceValues.abnormalWork
  })

  message.success(`已将选中的 ${selectedRows.value.length} 行批量赋值`)

  // 隐藏菜单
  const menu = document.getElementById('contextMenu') as HTMLElement
  if (menu) {
    menu.style.display = 'none'
  }
  })

}

const batchDeleteValues = () => {
  // 检查是否有选中的行
  if (selectedRows.value.length === 0) {
    message.error('请先选择行')
    return
  }
  
  // 批量删除选中的行
  message.confirm('确定要删除选中的行吗？').then(() => { 
  // 过滤掉选中的行（推荐做法）
  abnormalData.value = abnormalData.value.filter(
    row => !selectedRows.value.includes(row)
  )
  // 隐藏菜单
  const menu = document.getElementById('contextMenu') as HTMLElement
  if (menu) {
    menu.style.display = 'none'
  }
  })
}
</script>

<style lang="scss" scoped>
.mobile-batch-container {
  position: relative;
  z-index: 999;
}

.batch-assignment-sticky {
  position: sticky;
  top: 0;
  background: white;
  padding: 10px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  margin-bottom: 10px;
}

.card-list-container {
  max-height: calc(100vh - 220px); 
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.time-range-row {
  display: flex;
  align-items: center;

  .time-select {
    margin: 0 4px;
  }

  .time-range-separator {
    margin: 0 6px;
    font-size: 16px;
    color: #606266;
    line-height: 32px;
  }
}

.card-item {
  padding: 10px;
  margin-bottom: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 13px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  text-align: center;
}

@media (max-width: 768px) {
  .mobile-body-form :deep(.el-form-item__label) {
    text-align: right !important;
    width: 80px !important;
    display: inline-block;
  }
}

:deep(.no-spin-input) {

  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
    /* Firefox */
  }
}

/* 合并单元格的文本框样式 */
:deep(.el-table__cell) {
  .el-textarea {
    width: 100%;
    height: 100%;

    .el-textarea__inner {
      width: 100%;
      height: 100%;
      min-height: 120px;
      resize: vertical;
      border: none;
      box-shadow: none;
      padding: 8px;
      line-height: 1.5;
      font-size: 14px;
    }
  }
}

/* 确保合并的单元格高度自适应 */
:deep(.el-table__row) {
  .el-table__cell {
    vertical-align: top;

    &:has(.el-textarea) {
      padding: 0;
      height: auto;
    }
  }
}

/* 表格行高度自适应 */
:deep(.el-table__body-wrapper) {
  .el-table__row {
    height: auto;

    .el-table__cell {
      height: auto;
      min-height: 120px;
    }
  }
}

/* 右键菜单样式 */
.context-menu {
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 0;
  min-width: 120px;

  .menu-list {
    list-style: none;
    margin: 0;
    padding: 0;

    .menu-item {
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
      color: #606266;
      transition: background-color 0.3s;

      &:hover {
        background-color: #f5f7fa;
        color: #409eff;
      }

      &:first-child {
        border-radius: 4px 4px 0 0;
      }

      &:last-child {
        border-radius: 0 0 4px 4px;
      }

      &:only-child {
        border-radius: 4px;
      }
    }
  }
}
</style>