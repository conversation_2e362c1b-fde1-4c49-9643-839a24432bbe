{"name": "yudao-ui-admin-vue3", "version": "2.3.0-snapshot", "description": "基于vue3、vite4、element-plus、typesScript", "author": "xingyu", "private": false, "scripts": {"i": "pnpm install", "dev": "vite --mode env.local", "dev-server": "vite --mode dev", "ts:check": "vue-tsc --noEmit", "build:local": "node ./node_modules/vite/bin/vite.js build", "build:dev": "node ./node_modules/vite/bin/vite.js build --mode dev", "build:test": "node ./node_modules/vite/bin/vite.js build --mode test", "build:stage": "node ./node_modules/vite/bin/vite.js build --mode stage", "build:prod": "node ./node_modules/vite/bin/vite.js build --mode prod", "serve:dev": "vite preview --mode dev", "serve:prod": "vite preview --mode prod", "preview": "pnpm build:local && vite preview", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --fix \"./src/**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c "}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@form-create/designer": "^3.2.6", "@form-create/element-ui": "^3.2.11", "@iconify/iconify": "^3.1.1", "@microsoft/fetch-event-source": "^2.0.1", "@videojs-player/vue": "^1.0.0", "@vueuse/core": "^10.9.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.6.8", "benz-amr-recorder": "^1.1.5", "bpmn-js-token-simulation": "^0.10.0", "camunda-bpmn-moddle": "^7.0.1", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "diagram-js": "^12.8.0", "driver.js": "^1.3.1", "echarts": "^5.5.0", "echarts-wordcloud": "^2.1.0", "element-plus": "2.8.4", "fast-xml-parser": "^4.3.2", "grid-layout-plus": "^1.0.6", "highlight.js": "^11.9.0", "jsencrypt": "^3.3.2", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "markmap-common": "^0.16.0", "markmap-lib": "^0.16.1", "markmap-toolbar": "^0.17.0", "markmap-view": "^0.16.0", "min-dash": "^4.1.1", "mitt": "^3.0.1", "moment": "^2.30.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "qrcode": "^1.5.3", "qs": "^6.12.0", "steady-xml": "^0.1.0", "url": "^0.11.3", "video.js": "^7.21.5", "vue": "3.5.12", "vue-dompurify-html": "^4.1.4", "vue-i18n": "9.10.2", "vue-router": "^4.3.0", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "vxe-pc-ui": "^4.3.67", "vxe-table": "^4.12.1", "web-storage-cache": "^1.1.1", "xml-js": "^1.6.11"}, "devDependencies": {"@commitlint/cli": "^19.0.1", "@commitlint/config-conventional": "^19.0.0", "@iconify/json": "^2.2.187", "@intlify/unplugin-vue-i18n": "^2.0.0", "@purge-icons/generated": "^0.9.0", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.21", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.12", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "@unocss/eslint-config": "^0.57.4", "@unocss/transformer-variant-group": "^0.58.5", "@vitejs/plugin-legacy": "^5.3.1", "@vitejs/plugin-vue": "^5.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.17", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "consola": "^3.2.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.22.0", "lint-staged": "^15.2.2", "postcss": "^8.4.35", "postcss-html": "^1.6.0", "postcss-scss": "^4.0.9", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "rimraf": "^5.0.5", "rollup": "^4.12.0", "sass": "^1.69.5", "stylelint": "^16.2.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^14.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "terser": "^5.28.1", "typescript": "5.3.3", "unocss": "^0.58.5", "unplugin-auto-import": "^0.16.7", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.2", "vite": "5.1.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-ejs": "^1.7.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-top-level-await": "^1.4.4", "vue-eslint-parser": "^9.3.2", "vue-tsc": "^1.8.27"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://gitee.com/yudaocode/yudao-ui-admin-vue3"}, "bugs": {"url": "https://gitee.com/yudaocode/yudao-ui-admin-vue3/issues"}, "homepage": "https://gitee.com/yudaocode/yudao-ui-admin-vue3", "web-types": "./web-types.json", "engines": {"node": ">= 16.0.0", "pnpm": ">=8.6.0"}}