<template>
  <el-drawer
    v-model="drawerVisiable"
    :title="`${typeMap[formData.type!]}任务详情`"
    size="50%"
    :modal="false"
    direction="rtl"
    modal-class="no-modal-mask-layer"
    :before-close="beforeClose"
  >
    <el-tabs v-model="currentTab" @tab-change="tabChange">
      <el-tab-pane label="任务详情" name="form" />
      <el-tab-pane label="修改日志" name="log" />
    </el-tabs>
    <el-form label-width="120" size="small" v-if="currentTab === 'form'">
      <el-row>
        <el-col :span="24">
          <CardTitle title="基本信息" />
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务员">
            {{ formData?.seller }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户名称">
            {{ formData?.customerName }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="任务">
            {{ formData?.description }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="品号">
            {{ formData?.itemCode }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="品名">
            {{ formData?.itemName }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="规格">
            {{ formData?.customerName }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <CardTitle title="时间信息" />
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划资料接收日期">
            <el-date-picker
              v-model="formData.planReceiptDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                () => {
                  dateChange()
                  onDateChange(
                    cloneData.planReceiptDate!,
                    formData.planReceiptDate!,
                    'planReceiptDate',
                    '计划资料接收日期'
                  )
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际资料接收日期">
            <el-date-picker
              v-model="formData.actualReceiptDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
              @change="
                onDateChange(
                  cloneData.actualReceiptDate!,
                  formData.actualReceiptDate!,
                  'actualReceiptDate',
                  '实际资料接收日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划设计日期">
            <el-date-picker
              v-model="formData.planDesignDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                () => {
                  formData.type === 'logo' && dateChange()
                  onDateChange(
                    cloneData.planDesignDate!,
                    formData.planDesignDate!,
                    'planDesignDate',
                    '计划设计日期'
                  )
                }
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际设计日期">
            <el-date-picker
              v-model="formData.actualDesignDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
              @change="
                onDateChange(
                  cloneData.actualDesignDate!,
                  formData.actualDesignDate!,
                  'actualDesignDate',
                  '实际设计日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="formData.type === 'packing'">
          <el-form-item label="计划设计稿确认">
            <el-date-picker
              v-model="formData.planQuotationDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  cloneData.planQuotationDate!,
                  formData.planQuotationDate!,
                  'planQuotationDate',
                  '计划设计稿确认日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="formData.type === 'packing'">
          <el-form-item label="实际设计稿确认">
            <el-date-picker
              v-model="formData.actualQuotationDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
              @change="
                onDateChange(
                  cloneData.actualQuotationDate!,
                  formData.actualQuotationDate!,
                  'actualQuotationDate',
                  '实际设计稿确认'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="['program'].includes(formData.type!) ? '计划测试日期' : '计划打样日期'"
          >
            <el-date-picker
              v-model="formData.planTestingDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  cloneData.planTestingDate!,
                  formData.planTestingDate!,
                  'planTestingDate',
                  '计划打样日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            :label="['program'].includes(formData.type!) ? '实际测试日期' : '实际打样日期'"
          >
            <el-date-picker
              v-model="formData.actualTestingDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
              @change="
                onDateChange(
                  cloneData.actualTestingDate!,
                  formData.actualTestingDate!,
                  'actualTestingDate',
                  '实际打样日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!['program'].includes(formData.type!)">
          <el-form-item
            :label="
              ['packing', 'instruction'].includes(formData.type!) ? '计划确认日期' : '计划承认日期'
            "
          >
            <el-date-picker
              v-model="formData.planAdmitDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  cloneData.planAdmitDate!,
                  formData.planAdmitDate!,
                  'planAdmitDate',
                  '计划承认日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!['program'].includes(formData.type!)">
          <el-form-item
            :label="
              ['packing', 'instruction'].includes(formData.type!) ? '实际确认日期' : '实际承认日期'
            "
          >
            <el-date-picker
              v-model="formData.actualAdmitDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              :disabled-date="disabledDate"
              @change="
                onDateChange(
                  cloneData.actualAdmitDate!,
                  formData.actualAdmitDate!,
                  'actualAdmitDate',
                  '实际承认日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="12" v-if="!['program', 'packing'].includes(formData.type!)">
          <el-form-item label="计划变更日期">
            <el-date-picker
              v-model="formData.planChangeDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  cloneData.planChangeDate!,
                  formData.planChangeDate!,
                  'planChangeDate',
                  '计划变更日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!['program', 'packing'].includes(formData.type!)">
          <el-form-item label="实际变更日期">
            <el-date-picker
              v-model="formData.actualChangeDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  cloneData.actualChangeDate!,
                  formData.actualChangeDate!,
                  'actualChangeDate',
                  '实际变更日期'
                )
              "
            />
          </el-form-item>
        </el-col> -->
        <!-- <el-col :span="12" v-if="!['program', 'structure'].includes(formData.type!)">
          <el-form-item label="计划BOM日期">
            <el-date-picker
              v-model="formData.planBomDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  cloneData.planBomDate!,
                  formData.planBomDate!,
                  'planBomDate',
                  '计划BOM日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12" v-if="!['program', 'structure'].includes(formData.type!)">
          <el-form-item label="实际Bom日期">
            <el-date-picker
              v-model="formData.actualBomDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  cloneData.actualBomDate!,
                  formData.actualBomDate!,
                  'actualBomDate',
                  '实际Bom日期'
                )
              "
            />
          </el-form-item>
        </el-col> -->
        <el-col :span="12">
          <el-form-item label="计划完成日期">
            <el-date-picker
              v-model="formData.planCompleteDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit"
              @change="
                onDateChange(
                  cloneData.planCompleteDate!,
                  formData.planCompleteDate!,
                  'planCompleteDate',
                  '计划完成日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="实际完成日期">
            <el-date-picker
              v-model="formData.actualCompleteDate"
              type="date"
              value-format="YYYY-MM-DD"
              class="!w-100%"
              :disabled="!edit || !checkPermi(['allow:complete:date'])"
              :disabled-date="disabledDate"
              @change="
                onDateChange(
                  cloneData.actualCompleteDate!,
                  formData.actualCompleteDate!,
                  'actualCompleteDate',
                  '实际完成日期'
                )
              "
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="BOM计划完成日期">
            {{ formData?.planBomCompleteDate }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="formData.remark"
              type="textarea"
              placeholder="请输入内容"
              :disabled="!edit"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div v-infinite-scroll="load" style="overflow: auto; height: 59vh" v-else>
      <el-timeline>
        <el-timeline-item
          v-for="item in logList"
          :key="item.createTime"
          :timestamp="`${formatToDateTime(item.createTime)} - ${item.createName}`"
        >
          <div> 修改内容：{{ item.editContent }} </div>
          <div> 修改原因：{{ item.editReason }} </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    <template
      #footer
      v-if="
        currentTab === 'form' &&
        (permissions.includes(`order-bom:task:${formData.type}-edit`) ||
          permissions.includes('*:*:*'))
      "
    >
      <el-button type="warning" plain v-if="!edit" @click="edit = true">修改</el-button>
      <el-button type="primary" plain v-if="edit" @click="onSaveDetail()">保存</el-button>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import { cloneDeep } from 'lodash-es'
import { CustomerApi } from '@/api/report/erp/order-bom'
import { ElMessageBox } from 'element-plus'
import { formatToDateTime } from '@/utils/dateUtil'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import moment from 'moment'
import { checkPermi } from '@/utils/permission'

const { wsCache } = useCache()

const userInfo = wsCache.get(CACHE_KEY.USER)
const permissions = userInfo?.permissions || []

const drawerVisiable = ref(false)
const formData = ref({
  id: undefined,
  taskId: undefined,
  type: undefined,
  seller: undefined,
  customerName: undefined,
  description: undefined,
  custom: undefined,
  itemCode: undefined,
  itemName: undefined,
  spec: undefined,
  planReceiptDate: undefined,
  actualReceiptDate: undefined,
  planDesignDate: undefined,
  actualDesignDate: undefined,
  planQuotationDate: undefined,
  actualQuotationDate: undefined,
  planTestingDate: undefined,
  actualTestingDate: undefined,
  planAdmitDate: undefined,
  actualAdmitDate: undefined,
  planChangeDate: undefined,
  actualChangeDate: undefined,
  planBomDate: undefined,
  actualBomDate: undefined,
  planCompleteDate: undefined,
  actualCompleteDate: undefined,
  planBomCompleteDate: undefined,
  remark: undefined
})

const cloneData = ref({
  id: undefined,
  taskId: undefined,
  type: undefined,
  seller: undefined,
  customerName: undefined,
  description: undefined,
  itemCode: undefined,
  itemName: undefined,
  spec: undefined,
  planReceiptDate: undefined,
  actualReceiptDate: undefined,
  planDesignDate: undefined,
  actualDesignDate: undefined,
  planQuotationDate: undefined,
  actualQuotationDate: undefined,
  planTestingDate: undefined,
  actualTestingDate: undefined,
  planAdmitDate: undefined,
  actualAdmitDate: undefined,
  planChangeDate: undefined,
  actualChangeDate: undefined,
  planBomDate: undefined,
  actualBomDate: undefined,
  planCompleteDate: undefined,
  actualCompleteDate: undefined,
  remark: undefined
})
const edit = ref(false)

const typeMap = reactive({
  program: '程序',
  structure: '结构',
  packing: '包装',
  instruction: '说明书',
  logo: '面板'
})

const changeData = ref<any>({})
const loading = ref(false)
const message = useMessage()
const currentTab = ref('form')
const logList = ref<any[]>([])
const logTotal = ref(0)

const emits = defineEmits(['success'])

const onDateChange = (
  beforeValue: string,
  afterValue: string,
  field: string,
  fieldName: string
) => {
  if (field === 'planCompleteDate' && formData.value.planBomCompleteDate) {
    if (moment(formData.value.planBomCompleteDate).isBefore(moment(formData.value[field]))) {
      message.error('计划完成时间不能早于计划BOM完成时间')
      formData.value[field] = undefined
      return
    }
  }
  // 如果没有选择日期，则不执行后续操作
  if (!beforeValue && afterValue) return
  if ((!beforeValue && !afterValue) || beforeValue === afterValue) {
    delete changeData.value[field]
    return
  }
  changeData.value[field] = fieldName + '从' + beforeValue + '修改为' + afterValue
}
const openForm = (row: any) => {
  drawerVisiable.value = true
  formData.value = cloneDeep(row)
  cloneData.value = cloneDeep(row)
}

const tabChange = async () => {
  if (currentTab.value === 'log') {
    initLogList()
  }
}

const beforeClose = (done?: any) => {
  formData.value = {
    id: undefined,
    taskId: undefined,
    type: undefined,
    seller: undefined,
    customerName: undefined,
    description: undefined,
    custom: undefined,
    itemCode: undefined,
    itemName: undefined,
    spec: undefined,
    planReceiptDate: undefined,
    actualReceiptDate: undefined,
    planDesignDate: undefined,
    actualDesignDate: undefined,
    planQuotationDate: undefined,
    actualQuotationDate: undefined,
    planTestingDate: undefined,
    actualTestingDate: undefined,
    planAdmitDate: undefined,
    actualAdmitDate: undefined,
    planChangeDate: undefined,
    actualChangeDate: undefined,
    planBomDate: undefined,
    actualBomDate: undefined,
    planCompleteDate: undefined,
    actualCompleteDate: undefined,
    planBomCompleteDate: undefined,
    remark: undefined
  }
  cloneData.value = { ...formData.value }
  edit.value = false
  if (done) done()
}

const onSaveDetail = async () => {
  loading.value = true
  try {
    const modifyInfo = Object.values(changeData.value).join('\n')
    let reason = ''
    if (modifyInfo) {
      // 二次确认
      const { value } = await ElMessageBox.prompt('请输入修改原因', '修改原因', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        inputPattern: /^[\s\S]*.*\S[\s\S]*$/, // 判断非空，且非空格
        inputErrorMessage: '取消原因不能为空'
      })
      reason = value
    }
    await CustomerApi.saveDetail({
      ...formData.value,
      modifyInfo,
      reason
    })
    message.success('保存成功')
    drawerVisiable.value = false
    emits('success')
    beforeClose()
  } finally {
    loading.value = false
  }
}
const logQueryParams = reactive({
  detailId: formData.value.id,
  pageNo: 1,
  pageSize: 10
})
/** 查询列表 */
const getLogList = async () => {
  loading.value = true
  try {
    logQueryParams.detailId = formData.value.id
    const data = await CustomerApi.pageLog(logQueryParams)
    logList.value = logList.value.concat(data.list)
    logTotal.value = data.total
  } finally {
    loading.value = false
  }
}

const load = () => {
  logQueryParams.pageNo++
  if (logQueryParams.pageNo > Math.ceil(logTotal.value / logQueryParams.pageSize)) return
  getLogList()
}

const initLogList = () => {
  logQueryParams.pageNo = 1
  logList.value = []
  getLogList()
}

const dateChange = () => {
  const config = DATE_MAP[formData.value.type!][formData.value.custom!] as any
  for (let key in config) {
    formData.value[key] = moment(formData.value.planReceiptDate)
      .add('days', config[key])
      .format('YYYY-MM-DD')
    if (key == 'planCompleteDate' && formData.value.planBomCompleteDate) {
      if (moment(formData.value.planBomCompleteDate).isBefore(moment(formData.value[key]))) {
        message.error('计划完成时间不能早于计划BOM完成时间')
        formData.value[key] = undefined
      }
    }
  }
}

const DATE_MAP = {
  program: {
    '10': {
      planDesignDate: 0,
      planTestingDate: 3,
      planCompleteDate: 9
    },
    '11': {
      planDesignDate: 0,
      planTestingDate: 5,
      planCompleteDate: 14
    },
    '12': {
      planDesignDate: 0,
      planTestingDate: 5,
      planCompleteDate: 12
    },
    '13': {
      planDesignDate: 0,
      planTestingDate: 7,
      planCompleteDate: 17
    },
    '14': {
      planDesignDate: 0,
      planTestingDate: 9,
      planCompleteDate: 21
    },
    '15': {
      planDesignDate: 0,
      planTestingDate: 8,
      planCompleteDate: 19
    },
    '16': {
      planDesignDate: 0,
      planTestingDate: 8,
      planCompleteDate: 18
    }
  },
  structure: {
    '10': {
      planDesignDate: 0,
      planTestingDate: 1,
      planAdmitDate: 15,
      planChangeDate: 19,
      planCompleteDate: 20
    },
    '11': {
      planDesignDate: 0,
      planTestingDate: 1,
      planAdmitDate: 21,
      planChangeDate: 27,
      planCompleteDate: 30
    }
  },
  logo: {
    '10': {
      planReceiptDate: 0,
      planDesignDate: 0,
      planQuotationDate: 2,
      planTestingDate: 4,
      planAdmitDate: 8,
      planBomDate: 10,
      planCompleteDate: 11
    },
    '11': {
      planReceiptDate: 0,
      planDesignDate: 0,
      planQuotationDate: 2,
      planTestingDate: 4,
      planAdmitDate: 11,
      planBomDate: 13,
      planCompleteDate: 14
    },
    '12': {
      planReceiptDate: 0,
      planDesignDate: 0,
      planQuotationDate: 2,
      planTestingDate: 4,
      planAdmitDate: 5,
      planBomDate: 7,
      planCompleteDate: 8
    },
    '13': {
      planReceiptDate: 0,
      planDesignDate: 0,
      planQuotationDate: 2,
      planTestingDate: 4,
      planAdmitDate: 7,
      planBomDate: 9,
      planCompleteDate: 10
    },
    '14': {
      planReceiptDate: 0,
      planDesignDate: 0,
      planQuotationDate: 2,
      planTestingDate: 4,
      planAdmitDate: 17,
      planBomDate: 19,
      planCompleteDate: 20
    }
  },
  instruction: {
    '10': {
      planDesignDate: 0,
      planQuotationDate: 2,
      planTestingDate: 4,
      planAdmitDate: 8,
      planBomDate: 10,
      planCompleteDate: 11
    },
    '11': {
      planDesignDate: 0,
      planQuotationDate: 6,
      planTestingDate: 8,
      planAdmitDate: 12,
      planBomDate: 14,
      planCompleteDate: 15
    }
  },
  packing: {
    '10': {
      planDesignDate: 0,
      planQuotationDate: 2,
      planTestingDate: 4,
      planAdmitDate: 8,
      planBomDate: 10,
      planCompleteDate: 11
    },
    '11': {
      planDesignDate: 0,
      planQuotationDate: 4,
      planTestingDate: 6,
      planAdmitDate: 10,
      planBomDate: 12,
      planCompleteDate: 13
    },
    '12': {
      planDesignDate: 0,
      planQuotationDate: 4,
      planTestingDate: 6,
      planAdmitDate: 10,
      planBomDate: 12,
      planCompleteDate: 13
    },
    '13': {
      planDesignDate: 0,
      planQuotationDate: 6,
      planTestingDate: 8,
      planAdmitDate: 12,
      planBomDate: 14,
      planCompleteDate: 15
    }
  }
}

const disabledDate = (data:Date)=>{
  return moment(data).isSameOrBefore(moment().add(-1, 'days'))
}

defineExpose({
  openForm
})
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 10px !important;
}
:deep(.el-form-item__content) {
  width: 100%;
  background-color: #f5f7f9;
  padding: 1px 5px;
}
:deep(.el-input__wrapper),
:deep(.el-select__wrapper) {
  box-shadow: none !important;
  border-radius: 0;
}
:deep(.el-input__inner) {
  color: #585858 !important;
  -webkit-text-fill-color: #585858 !important;
}

:deep(.el-timeline-item__content) {
  white-space: pre-wrap;
  font-size: 1rem;
  color: var(--regular-text-color);
  background-color: #f8fcff;
  border-radius: 5px;
  padding: 5px;
}
.el-timeline-item__timestamp {
  padding: 3px;
  font-size: 1rem;

  .el-timeline-item__user {
    color: var(--el-color-warning);
    font-size: 1rem;
  }
}
</style>
