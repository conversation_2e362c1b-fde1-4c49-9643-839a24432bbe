import request from '@/config/axios'

export const CustomerApi = {
  /** 获取客户信息 */
  getCustomerList: async (params: any) => {
    return await request.get({ url: '/butt-joint/erp/project/get-customer', params })
  },
  /** 获取料品信息 */
  getItemList: async (itemCode: string) => {
    return await request.get({ url: '/butt-joint/erp/project/get-item', params: { itemCode } })
  },
  /** 批量保存任务 */
  batchSaveTask: async (data: any[]) => {
    return await request.post({ url: '/eng/bom-task/batch-save', data })
  },
  /** 分页查询订单BOM事务 */
  pageTask: async (data: any) => {
    return await request.post({ url: '/eng/bom-task/page', data })
  },
  /** 分页查询订单 BOM 事务详情 */
  pageDetail: async (data: any) => {
    return await request.post({ url: '/eng/bom-task/page-detail', data })
  },
  /** 保存任务详情 */
  saveDetail: async (data: any) => {
    return await request.post({ url: '/eng/bom-task/save-detail', data })
  },
  /** 查询修改日志 */
  pageLog: async (params: any) => {
    return await request.get({ url: '/eng/bom-task/page-log', params })
  },
  /** 导出事务 */
  exportDetail: (data: any) => {
    return request.downloadPost({ url: '/eng/bom-task/export-detail', data })
  },
  /** 导出事务 */
  export: (data: any) => {
    return request.downloadPost({ url: '/eng/bom-task/export', data })
  }
}
