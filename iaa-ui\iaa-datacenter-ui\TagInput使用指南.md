# AI Agent 流程框架 - Tag输入组件使用指南

## 概述

我已经为您实现了一个支持文本和标签混合输入的Vue 3组件，特别适用于AI Agent流程框架中的输入场景。该组件支持使用大括号 `{tag}` 格式自动解析为标签，并且可以像提及输入框那样按退格键整体删除标签。

## 功能特性

✅ **文本和标签混合输入** - 支持在同一个输入框中混合输入普通文本和标签  
✅ **自动标签解析** - 使用 `{tag}` 格式自动解析为可视化标签  
✅ **智能退格删除** - 按退格键可以整体删除标签（类似@提及功能）  
✅ **多种标签类型** - 支持用户、系统、错误、AI、数据等预设标签类型  
✅ **自定义样式** - 支持自定义标签的颜色和样式  
✅ **粘贴支持** - 支持粘贴纯文本并保持标签格式  
✅ **实时解析** - 实时解析为结构化数据，便于后续处理  
✅ **响应式设计** - 适配移动端和桌面端  
✅ **TypeScript支持** - 完整的类型定义

## 文件结构

```
src/
├── components/
│   └── TagInput/
│       ├── index.vue          # 主组件文件
│       └── README.md          # 详细文档
├── views/
│   ├── TagInputDemo.vue       # 演示页面
│   └── Home/
│       └── text.vue           # 原始测试文件（已改进）
└── router/
    └── modules/
        └── remaining.ts       # 路由配置（已添加演示页面）
```

## 快速开始

### 1. 启动项目

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm run dev
```

### 2. 访问演示页面

启动项目后，在浏览器中访问：`http://localhost:端口号/tag-input-demo`

### 3. 基础使用

```vue
<template>
  <TagInput 
    v-model="inputValue"
    placeholder="输入内容，使用 {tag} 表示标签"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import TagInput from '@/components/TagInput/index.vue'

const inputValue = ref('请处理 {user:张三} 的数据请求')

const handleChange = (parsedContent) => {
  console.log('解析结果:', parsedContent)
}
</script>
```

## 标签类型示例

### 用户标签（绿色）
```
{user:张三}
{user:管理员}
```

### 系统标签（橙色）
```
{system:数据处理}
{system:用户验证}
```

### 错误标签（红色）
```
{error:连接失败}
{error:数据异常}
```

### AI标签（紫色）
```
{ai:GPT-4}
{ai:语言模型}
```

### 数据标签（青色）
```
{data:用户行为}
{data:数据库}
```

### 普通标签（蓝色）
```
{普通标签}
{任意内容}
```

## AI Agent 流程示例

```
AI Agent 接收到 {user:用户} 的请求，首先调用 {system:数据验证} 模块，然后使用 {ai:语言模型} 进行分析，最后将结果存储到 {data:数据库} 中
```

## 组件API

### Props
- `modelValue: string` - 输入框的值，支持v-model
- `placeholder: string` - 占位符文本
- `tagStyles: Record<string, string>` - 自定义标签样式（预留）

### Events
- `update:modelValue` - 输入值变化时触发
- `change` - 解析结果变化时触发，返回结构化数据

### 解析结果格式
```typescript
interface ParsedItem {
  type: 'text' | 'tag'
  value: string
}
```

## 键盘操作

- **退格键（Backspace）**：在标签内部或标签前面按退格键，会整体删除标签
- **粘贴（Ctrl+V）**：支持粘贴纯文本，保持 `{tag}` 格式

## 样式自定义

可以通过CSS覆盖默认样式：

```css
.tag.tag-custom {
  background: #your-color;
  color: #your-text-color;
  border-color: rgba(your-color, 0.3);
}
```

## 注意事项

1. 标签格式必须是 `{内容}` 的形式，大括号内不能包含其他大括号
2. 组件使用 `contenteditable` 实现，在某些浏览器中可能有兼容性差异
3. 建议在表单验证时检查解析后的结构化数据

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 下一步

1. 启动项目：`pnpm run dev`
2. 访问演示页面查看效果
3. 根据需要在您的页面中引入和使用TagInput组件
4. 参考演示页面的代码进行定制化开发

如果您需要任何修改或有其他问题，请随时告诉我！
