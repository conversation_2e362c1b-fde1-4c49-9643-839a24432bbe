<template>
  <el-drawer
    v-model="visiable"
    :modal="false"
    :wrapperClosable="false"
    :with-header="false"
    modal-class="AIdrawer"
    class="AIdrawerWrapper !w-800px !h-100% position-relative !left-100vw"
    direction="rtl"
  >
    <div class="position-fixed right-800px top-45vh">
      <el-button type="danger" class="!w-30px !h-50px" @click="close">X</el-button>
    </div>
    <slot name="content"></slot>
  </el-drawer>
</template>
<script lang="ts" setup>
import { propTypes } from '@/utils/propTypes'

const props = defineProps({
  modelValue: propTypes.bool.def(false)
})

const visiable = ref(props.modelValue)

watch(
  () => props.modelValue,
  (val) => {
    visiable.value = val
  }
)

const emit = defineEmits(['update:modelValue'])

const close = () => {
  visiable.value = false
  emit('update:modelValue', false)
}
</script>

<style>
/* 抽屉从右向左 */
.AIdrawerWrapper {
  margin-left: auto;
}

/* mask蒙版的宽度 */
.AIdrawer {
  width: 0px;
}
</style>
