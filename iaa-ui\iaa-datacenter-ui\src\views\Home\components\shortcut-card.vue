<template>
  <card-title
    title="快捷入口"
  />
  <div class="flex items-center justify-around flex-wrap">
    <div
      class="shortcut w-140px"
      v-for="(router, index) in routers"
      :key="index"
      @click="push({ name: router.name })"
    >
      <div class="shortcut-icon">
        <Icon :icon="router.meta?.icon" :size="30" color="var(--el-color-primary)" />
      </div>
      <div class="ml-2px parent-name">
        {{ router.meta?.parentName }}
      </div>
      <div class="ml-2px">
        {{ router.meta?.title }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { usePermissionStore } from '@/store/modules/permission'
const permissionStore = usePermissionStore()

const { push } = useRouter()

const exists = reactive([
  'Redirect',
  'Home',
  'UserInfo',
  'dict',
  'CodegenEdit',
  'JobL',
  'Login',
  'SSOLogin',
  'SocialLogin',
  'NoAccess',
  'NoFound',
  'Error',
  'bpm',
  '/system',
  '/infra',
  '/report'
])

// 递归函数，获取最深层级的路由
function getDeepestRoutes(
  routes: any[],
  parentName: string = '',
  currentDepth: number = 0,
  maxDepth: number = 0,
  deepestRoutes: any[] = []
) {
  routes.forEach((route) => {
    if (route.children && route.children.length > 0) {
      getDeepestRoutes(
        route.children,
        route?.meta?.title,
        currentDepth + 1,
        maxDepth,
        deepestRoutes
      )
    } else {
      if (currentDepth >= maxDepth) {
        route.meta.parentName = parentName
        deepestRoutes.push(route)
      }
    }
  })
  return deepestRoutes
}

// // 递归函数，根据名称查找路由
// function findRouteByName(routes: any[], targetName: string): any | null {
//   for (const route of routes) {
//     if (route.name === targetName) {
//       return route
//     }
//     if (route.children && route.children.length > 0) {
//       const foundRoute = findRouteByName(route.children, targetName)
//       if (foundRoute) {
//         return foundRoute
//       }
//     }
//   }
//   return null
// }

const routers = computed(() => {
  let menu = permissionStore.getRouters.filter(
    (router) => !exists.includes(router.name) && router.name
  )
  return getDeepestRoutes(menu)
})
</script>

<style lang="scss" scoped>
.shortcut {
  text-align: center;
  color: var(--el-color-info);
  padding: 10px;
  font-size: 16px;
  cursor: pointer;
  .shortcut-icon {
    background-color: var(--el-color-primary-light-9);
    border-radius: 10px;
    display: inline-block;
    width: 80%;
    height: 60px;
    line-height: 60px;
    padding-top: 10px;
  }

  .parent-name {
    font-size: 14px;
    color: var(--el-color-primary-light-3);
  }
}

.shortcut:hover{
  background-color: #fafafa;
  border-radius: 10px;
}
</style>
