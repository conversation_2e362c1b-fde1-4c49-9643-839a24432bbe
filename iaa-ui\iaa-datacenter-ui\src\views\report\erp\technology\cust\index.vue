<template>
  <ContentWrap>
    <div class="pc-view" v-if="!mobile">
      <vxe-toolbar custom ref="toolbarRef" size="mini">
        <!-- <template #tools>
      <el-button circle @click="uploadRef?.open()" size="small" class="mr-5px !h-28px !w-28px">
        <Icon icon="ep:upload" />
      </el-button>
      <el-button circle @click="exportLinkAll" size="small" class="mr-5px !h-28px !w-28px">
        <Icon icon="ep:download" />
      </el-button>
    </template> -->
      </vxe-toolbar>
      <div class="h-[calc(100vh-260px)]">
        <vxe-table
:row-config="{ height: 30}"
          ref="tableRef"
          :data="list"
          :header-cell-style="{ padding: 0 }"
          :cell-style="{ padding: 0, height: '30px', color: '#232323' }"
          :filter-config="{ showIcon: false }"
          border
          stripe
          show-overflow
          align="center"
          :loading="loading"
          height="100%"
          :menu-config="menuConfig"
          @menu-click="menuClickEvent"
          :checkbox-config="{ labelField: 'name', highlight: true, range: true }"
          :edit-config="{ trigger: 'manual', mode: 'row', autoClear: false }"
        >
          <vxe-column type="checkbox" width="60" field="name" />
          <vxe-column title="报工数量" width="200" field="reportNum" :edit-render="{}">
            <template #default="{ row }">
              {{ row.reportNum }}
            </template>
            <template #edit="{ row }">
              <el-input-number v-model="row.reportNum" :min="0" :precision="2" />
            </template>
          </vxe-column>
          <vxe-column title="工时" width="200" field="hours" :edit-render="{}">
            <template #default="{ row }">
              {{ row.hours }}
            </template>
            <template #edit="{ row }">
              <el-input-number v-model="row.hours" :min="0" :precision="2" />
            </template>
          </vxe-column>
          <vxe-column field="orderCode" width="150">
            <template #header>
              <div>订单号</div>
              <el-input
                v-model="queryParams.orderCode"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>

          <vxe-column field="customers" width="100">
            <template #header>
              <div>客户</div>
              <el-input
                v-model="queryParams.customers"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column field="salesman" width="100">
            <template #header>
              <div>业务员</div>
              <el-input
                v-model="queryParams.salesman"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column field="line" width="100">
            <template #header>
              <div>产线</div>
              <el-input
                v-model="queryParams.line"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column field="workOrderCode" width="200">
            <template #header>
              <div>工单号</div>
              <el-input
                v-model="queryParams.workOrderCode"
                @change="handleList"
                clearable
                placeholder="按回车筛选"
                style="width: 100%"
                size="small"
              />
            </template>
          </vxe-column>
          <vxe-column title="订单数量" width="100" field="orderNum" />
          <vxe-column title="工单数量" width="100" field="workOrderNum" />
          <vxe-column title="工序" width="160" field="process" />
          <vxe-column title="品号" width="160" field="productNo" />
          <vxe-column title="品名" width="160" field="productName" />
          <vxe-column title="规格" width="160" field="specifications" />
          <vxe-column title="订单累计报工数量" width="160" field="orderTotalReportNum" />
          <vxe-column title="订单累计报工工时" width="160" field="orderTotalReportHours" />
          <vxe-column title="报工时间" width="160" field="reportTime">
            <template #default="{ row }">
              {{ row.reportTime && formatToDateTime(row.reportTime) }}
            </template>
          </vxe-column>
          <vxe-column title="报工人" width="160" field="reportUser" />
          <vxe-column
            title="操作"
            width="160"
            fixed="right"
            v-hasPermi="['technology:declaration:edit']"
          >
            <template #default="{ row }">
              <template v-if="hasEditStatus(row)">
                <el-button @click="saveRowEvent(row)" link>保存</el-button>
                <el-button @click="cancelRowEvent()" link>取消</el-button>
              </template>
              <template v-else>
                <el-button @click="editRowEvent(row)" link>编辑</el-button>
                <!-- <el-button @click="deleteRowEvent(row)" link>删除</el-button> -->
              </template>
            </template>
          </vxe-column>
        </vxe-table>
      </div>
      <!-- 分页 -->
      <Pagination
        :total="total"
        v-model:page="queryParams.pageNo"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>
    <div class="h-[calc(100vh-110px)]" v-else>
      <div class="h-40px">
        <el-input
          v-model="queryParams.all"
          size="large"
          :suffix-icon="Search"
          placeholder="全域查询"
          clearable
          @change="handleList"
        />
      </div>
      <div
        class="h-[calc(100%-75px)] overflow-auto"
        v-infinite-scroll="load"
        :infinite-scroll-distance="20"
        :infinite-scroll-immediate="disabled"
        v-loading="loading"
      >
        <div v-for="item in list" :key="item.id" class="p-10px data-item position-relative">
          <div
            class="position-absolute top-5px right-5px"
            v-hasPermi="['technology:declaration:edit']"
          >
            <el-button type="primary" circle plain @click="showEdit(item)">
              <Icon icon="ep:edit" />
            </el-button>
          </div>
          <!-- <div
            class="position-absolute top-5px right-5px"
            v-hasPermi="['technology:declaration:edit']"
          >
            <el-button type="danger" circle plain @click="deleteRowEvent(item)">
              <Icon icon="ep:delete" />
            </el-button>
          </div> -->
          <CardTitle :title="`工单：${item.workOrderCode};数量：${item.workOrderNum}`" />
          <el-form size="small">
            <el-row>
              <el-col :span="12">
                <el-form-item label="品号">{{ item.productNo }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="品名">{{ item.productName }}</el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="规格">{{ item.specifications }}</el-form-item>
            <el-row>
              <el-col :span="12">
                <el-form-item label="报工数量">{{ item.reportNum }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="累计数量">{{ item.orderTotalReportNum }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="报工工时">{{ item.hours }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="累计工时">{{ item.orderTotalReportHours }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工序">
                  <el-tag>{{ item.process }}</el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="报工人">{{ item.reportUser }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="时间">{{ item.reportTime }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产线">{{ item.line }}</el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
      <div class="h-30px leading-30px border-t-#a8a8a8">共计：{{ total }}条记录</div>
    </div>
    <Dialog title="按选中数据设置报工数量和工时" v-model="selectionVisiable">
      <div>当前选中：{{ selectionData.length }} 条数据</div>
      <el-form label-width="100px">
        <el-form-item label="设置报工数量">
          <el-input-number v-model="reportNumConsuming" :min="0" :precision="2" class="!w-100%" />
        </el-form-item>
        <el-form-item label="设置工时">
          <el-input-number v-model="hoursConsuming" :min="0" :precision="2" class="!w-100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" @click="onSaveSelection">保存</el-button>
      </template>
    </Dialog>

    <el-drawer title="修改报工数量及工时" v-model="edit" direction="btt" size="50%">
      <el-form label-width="90px">
        <el-form-item label="工单">
          {{ editRow.workOrderCode }}
        </el-form-item>
        <el-form-item label="工序">
          <el-tag>{{ editRow.process }}</el-tag>
        </el-form-item>
        <el-form-item label="报工数量">
          <el-input-number v-model="editRow.reportNum" class="!w-100%" />
        </el-form-item>
        <el-form-item label="工时">
          <el-input-number v-model="editRow.hours" class="!w-100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button type="primary" class="!w-100%" @click="saveRow(editRow)">保存</el-button>
      </template>
    </el-drawer>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { CustApi } from '@/api/report/technology/cust'
import { cloneDeep } from 'lodash-es'
import { formatToDateTime } from '@/utils/dateUtil'
import { useAppStore } from '@/store/modules/app'
import { Search } from '@element-plus/icons-vue'

const tableRef = ref()
const toolbarRef = ref()
const selectionVisiable = ref(false)
const selectionData = ref<any[]>([])
const reportNumConsuming = ref<number | undefined>(undefined)
const hoursConsuming = ref<number | undefined>(undefined)

const conditionsVisiable = ref(false)
const appStore = useAppStore()

const mobile = computed(() => appStore.getMobile)

const queryParams = ref({
  pageNo: 1,
  pageSize: 20,
  orderCode: '',
  customers: '',
  workOrderCode: '',
  salesman: '',
  line: undefined,
  all:''
})

const total = ref(0)
const list = ref<any[]>([])
const loading = ref(false)
const message = useMessage()

const menuConfig = reactive<any>({
  body: {
    options: [[{ code: 'selected', name: '按选中设置报工数量和工时' }]]
  }
})

const menuClickEvent = ({ menu }) => {
  const $table = tableRef.value
  if (!$table) return
  switch (menu.code) {
    case 'selected':
      const rows = $table.getCheckboxRecords()
      if (rows.length === 0) {
        message.alertError('请选择要设置的数据')
        selectionData.value = []
        return
      }
      selectionData.value = rows
      selectionVisiable.value = true
      break
    case 'conditions':
      conditionsVisiable.value = true
      break
  }
}
const handleList = () => {
  queryParams.value.pageNo = 1
  if (mobile.value) {
    queryParams.value.pageSize = 10
  } else {
    queryParams.value.pageSize = 20
  }
  getList()
}

const edit = ref(false)
const editRow = ref({
  id: undefined,
  reportNum: undefined,
  hours: undefined,
  workOrderCode: '',
  process: ''
})

const disabled = ref(false)

const showEdit = (row: any) => {
  editRow.value.id = row.id
  editRow.value.reportNum = row.reportNum
  editRow.value.hours = row.hours
  editRow.value.workOrderCode = row.workOrderCode
  editRow.value.process = row.process
  edit.value = true
}
const load = async () => {
  loading.value = true
  try {
    const maxSize = Math.ceil(total.value / queryParams.value.pageSize)

    if (queryParams.value.pageNo >= maxSize) {
      disabled.value = true
      return
    }
    queryParams.value.pageNo += 1
    let query = cloneDeep(queryParams.value)
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }
    const res = await CustApi.getCustDeclarationPage(query)
    list.value = list.value.concat(res.list)
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const saveRow = async (row: any) => {
  loading.value = true
  try {
    let data = {
      id: row.id,
      reportNum: row.reportNum,
      hours: row.hours
    }
    await CustApi.update([data])
    message.success('保存成功')
    getList()
    edit.value = false
    editRow.value = {
      id: undefined,
      reportNum: undefined,
      hours: undefined,
      workOrderCode: '',
      process: ''
    }
  } finally {
    loading.value = false
  }
}

const getList = async () => {
  loading.value = true
  try {
    let query = cloneDeep(queryParams.value)
    for (let key in query) {
      if (Array.isArray(query[key])) {
        query[key] = `${query[key].join(',')}`
      }
    }
    const res = await CustApi.getCustDeclarationPage(query)
    list.value = res.list
    total.value = res.total
  } finally {
    loading.value = false
  }
}

const onSaveSelection = async () => {
  loading.value = true
  let boolPermissions = true
  try {
    let data: any[] = []
    selectionData.value.forEach((item: any) => {
      data.push({
        id: item.id,
        reportNum: reportNumConsuming.value,
        hours: hoursConsuming.value
      })
    })
    if (boolPermissions) {
      await CustApi.update(data)
      message.success('保存成功')
      getList()
      selectionVisiable.value = false
      selectionData.value = []
      reportNumConsuming.value = undefined
      hoursConsuming.value = undefined
    }
  } finally {
    loading.value = false
  }
}

const hasEditStatus = (row: any) => {
  const $table = tableRef.value
  if ($table) {
    return $table.isEditByRow(row)
  }
}

const editRowEvent = (row: any) => {
  const $table = tableRef.value
  if ($table) {
    $table.setEditRow(row)
  }
}

// const deleteRowEvent = async (row: any) => {
//   // 删除的二次确认
//   await message.delConfirm()
//   await CustApi.delete(row.id)
//   getList()
// }

const saveRowEvent = (row: any) => {
  const $table = tableRef.value
  loading.value = true
  try {
    if ($table) {
      $table.clearEdit().then(async () => {
        let data = {
          id: row.id,
          reportNum: row.reportNum,
          hours: row.hours
        }
        await CustApi.update([data])
        message.success('保存成功')
        getList()
      })
    }
  } finally {
    loading.value = false
  }
}

const cancelRowEvent = () => {
  const $table = tableRef.value
  if ($table) {
    $table.clearEdit()
  }
}

onMounted(() => {
  handleList()
  nextTick(() => {
    unref(tableRef)?.connect(unref(toolbarRef))
  })
})
</script>

<style lang="scss" scoped>
:deep(.vxe-header--column .vxe-cell) {
  padding: 0 !important;
}

:deep(.row--stripe) {
  background-color: #f9f9f9 !important;
}

:deep(.vxe-cell--edit-icon) {
  display: none !important;
}

@media (max-width: 768px) {
  :deep(.el-card__body) {
    padding: 0 !important;
  }

  .data-item {
    padding-right: 30px;
    // 弱化阴影参数
    box-shadow: 0 2px 6px rgba(0, 85, 255, 0.1); // 更淡的蓝紫色阴影
    transition: box-shadow 0.2s ease; // 缩短过渡时间
  }

  :deep(.el-form-item) {
    margin-bottom: 5px;
  }

  :deep(.el-form-item__label) {
    color: #a8a8a8 !important;
  }

  :deep(.el-form-item__content) {
    display: block;
    max-width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
