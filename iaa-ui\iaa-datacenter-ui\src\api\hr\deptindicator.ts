import request from '@/config/axios'

// 部门指标 VO
export interface DeptIndicatorVO {
  id: number // 指标ID
  dept: string // 部门
  year: number // 年
  month: number // 月
  indicator: string // 指标名称
  value: number // 指标值
}

// 部门指标 API
export const DeptIndicatorApi = {
  // 查询部门指标分页
  getDeptIndicatorPage: async (params: any) => {
    return await request.get({ url: `/hr/dept-indicator/page`, params })
  },
  getDeptIndicatorList: async (params: any) => {
    return await request.get({ url: `/hr/dept-indicator/list`, params })
  },

  // 查询部门指标详情
  getDeptIndicator: async (params: any) => {
    return await request.get({ url: `/hr/dept-indicator/get`, params })
  },

  saveDeptIndicator: async (data: any) => {
    return await request.post({ url: `/hr/dept-indicator/save`, data })
  },

  // 删除部门指标
  deleteDeptIndicator: async (params: any) => {
    return await request.delete({ url: `/hr/dept-indicator/delete`, params })
  },

  // 导出部门指标 Excel
  exportDeptIndicator: async (params) => {
    return await request.download({ url: `/hr/dept-indicator/export-excel`, params })
  },
  //获取信息部指标列表
  getInformationList:(params)=>{
    return  request.get({url:`/hr/dept-indicator/list-information`,params})
  },
  // 更新信息部指标
  updateInformation:(data)=>{
    return request.post({url:`/hr/dept-indicator/update-information`,data})
  },
}
